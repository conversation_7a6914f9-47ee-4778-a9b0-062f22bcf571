#!/bin/bash

function install_log_agent() {
    local SOURCE=$1
    local LOGFILE=$2
    local API_ENDPOINT="http://intellilog.baidu-int.com/api/v1/log-agent"
    local RESPONSE

    RESPONSE=$(curl -s --connect-timeout 1 --max-time 1 "${API_ENDPOINT}")
    if [[ $? -ne 0 ]]; then
        echo "Failed to fetch data from API endpoint"
        return 0
    fi

    local TOKEN=$(echo "${RESPONSE}" | grep -o '"token":"[^"]*"' | sed 's/"token":"//;s/"//')
    local VERSION=$(echo "${RESPONSE}" | grep -o '"version":"[^"]*"' | sed 's/"version":"//;s/"//')

    if [[ -z "${TOKEN}" || -z "${VERSION}" ]]; then
        echo "Failed to parse TOKEN, VERSION from API response"
        return 0
    fi
    
    local DIR="./log-agent-v${VERSION}"
    local TARFILE="./log-agent-v${VERSION}.tar.gz"

    pids=$(ps aux | grep log-agent | grep -v grep | awk '{print $2}')
    if [ -z "$pids" ]; then
        echo "No processes found containing ${DIR}."
    else
        echo "Killing processes: $pids"
        kill -9 $pids
        echo "Processes killed."
    fi

    if [[ ! -d ${DIR} ]]; then
        if [[ ! -f ${TARFILE} ]]; then
            wget -O ${TARFILE} --no-check-certificate --header "${TOKEN}" \
            "https://irepo.baidu-int.com/rest/prod/v3/baidu/ep-qa/log-agent/releases/${VERSION}/files" || {
                echo "Failed to download ${TARFILE}"
                return 1
            }
        fi
        mkdir ${DIR}
        tar zxf ${TARFILE} -C ${DIR} || {
            echo "Failed to extract ${TARFILE}"
            return 1
        }
    fi

    if [[ ! -z "${LOGFILE}" ]]; then
        sed -i "s|path = \".*\"|path = \"${LOGFILE}\"|" "${DIR}/output/conf/logs.toml"
    fi
    # 修改配置
    ${DIR}/output/supervise -f "nohup ${DIR}/output/bin/log-agent --source=${SOURCE} --configs=../conf > ${DIR}/output/agent.log 2>&1 &"  -p ${DIR}/output/status/log-agent.pid  &>/dev/null &

    for old_dir in ./log-agent-v*; do
        if [[ ${old_dir} != ${DIR} ]] && [[ ${old_dir} != ${TARFILE} ]]; then
            rm -rf ${old_dir}
        fi
    done
    echo "log-agent installed successfully"
}

(install_log_agent "req-analyzer" "/home/<USER>/categraf/baidu/ep-qa/log-agent/b.log")