# 项目名称
log-agent采集

# 平台安装脚本
```shell
function install_log_agent() {
    local SOURCE=$1
    local API_ENDPOINT="http://intellilog.baidu-int.com/api/v1/log-agent"
    local RESPONSE

    RESPONSE=$(curl -s --connect-timeout 1 --max-time 1 --retry 1 "${API_ENDPOINT}")
    if [[ $? -ne 0 ]]; then
        echo "Failed to fetch data from API endpoint"
        return 0
    fi

    local TOKEN=$(echo "${RESPONSE}" | grep -oP '"token":\s*"\K([^"]+)' | head -n 1)
    local VERSION=$(echo "${RESPONSE}" | grep -oP '"version":\s*"\K([^"]+)' | head -n 1)

    if [[ -z "${TOKEN}" || -z "${VERSION}" ]]; then
        echo "Failed to parse TOKEN, VERSION from API response"
        return 0
    fi
    
    local DIR="./log-agent-v${VERSION}"
    local TARFILE="./log-agent-v${VERSION}.tar.gz"

    pids=$(ps aux | grep log-agent | grep -v grep | awk '{print $2}')
    if [ -z "$pids" ]; then
        echo "No processes found containing ${DIR}."
    else
        sleep 1
        echo "Killing processes: $pids"
        kill -9 $pids
        echo "Processes killed."
    fi

    if [[ ! -d ${DIR} ]]; then
        if [[ ! -f ${TARFILE} ]]; then
            wget -O ${TARFILE} --no-check-certificate --header "${TOKEN}" \
            "https://irepo.baidu-int.com/rest/prod/v3/baidu/ep-qa/log-agent/releases/${VERSION}/files" || {
                echo "Failed to download ${TARFILE}"
                return 1
            }
        fi
        mkdir ${DIR}
        tar zxf ${TARFILE} -C ${DIR} || {
            echo "Failed to extract ${TARFILE}"
            return 1
        }
    fi
    ${DIR}/output/supervise -f "nohup ${DIR}/output/bin/log-agent --source=${SOURCE} --configs=../conf > ${DIR}/output/agent.log 2>&1 &"  -p ${DIR}/output/status/log-agent.pid  &>/dev/null &

    for old_dir in ./log-agent-v*; do
        if [[ ${old_dir} != ${DIR} ]] && [[ ${old_dir} != ${TARFILE} ]]; then
            rm -rf ${old_dir}
        fi
    done
    echo "log-agent installed successfully"
}

#示例 nb-server的noah_control 中start时，增加log-agent部署
function start() {
    #----------------------开始部署log-agent-----------------------
    # 注意!!!
    # 按照自身平台修改来源 当前入参支持 nb-core nb-server ebt cardan flowcloud flowcloudserver
    (install_log_agent "cardan") 
    #----------------------部署完成-----------------------

    # 业务平台代码
    echo "starting ${SERVICE_NAME} service..."
    start_the_service
    get_service_num
    if [[ $? -eq 0 ]]
    then
        echo "start ${SERVICE_NAME} service failed..."
        exit 1
    fi
    
    echo "start ${SERVICE_NAME} service done..."
    return 0
}
```
