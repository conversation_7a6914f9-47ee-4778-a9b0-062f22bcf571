module icode.baidu.com/baidu/ep-qa/log-agent

go 1.23.8

require (
	github.com/chai2010/winsvc v0.0.0-20200705094454-db7ec320025c
	github.com/coreos/go-systemd v0.0.0-20191104093116-d3cd4ed1dbcf
	github.com/gin-gonic/gin v1.9.1
	github.com/gobwas/glob v0.2.3
	github.com/gogo/protobuf v1.3.2
	github.com/golang/protobuf v1.5.4
	github.com/golang/snappy v0.0.4
	github.com/json-iterator/go v1.1.12
	github.com/koding/multiconfig v0.0.0-20171124222453-69c27309b2d7
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/prometheus/client_model v0.6.0 // indirect
	github.com/prometheus/common v0.47.0
	github.com/prometheus/prometheus v0.40.0
	github.com/stretchr/testify v1.9.0
	github.com/toolkits/pkg v1.3.7
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

require (
	github.com/alecthomas/participle v0.4.1 // indirect
	github.com/awnumar/memcall v0.2.0 // indirect
	github.com/bufbuild/protocompile v0.14.1 // indirect
	github.com/bytedance/sonic v1.9.1 // indirect
	github.com/chenzhuoyu/base64x v0.0.0-20221115062448-fe3a3abad311 // indirect
	github.com/gabriel-vasile/mimetype v1.4.2 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/klauspost/cpuid/v2 v2.2.4 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	golang.org/x/arch v0.3.0 // indirect
	golang.org/x/crypto v0.23.0 // indirect
	golang.org/x/sys v0.21.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240604185151-ef581f913117 // indirect
	google.golang.org/grpc v1.65.0 // indirect
)

require (
	github.com/IBM/sarama v1.42.1
	github.com/awnumar/memguard v0.22.4
	github.com/bmatcuk/doublestar/v3 v3.0.0
	github.com/jhump/protoreflect v1.17.0
	github.com/kardianos/service v1.2.2
	github.com/sleepinggenius2/gosmi v0.4.4
	golang.org/x/net v0.25.0
	golang.org/x/text v0.15.0
)

replace gopkg.in/yaml.v2 => github.com/rfratto/go-yaml v0.0.0-20211119180816-77389c3526dc

require (
	github.com/BurntSushi/toml v1.4.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc
	github.com/eapache/go-resiliency v1.4.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/fatih/camelcase v1.0.0 // indirect
	github.com/fatih/structs v1.1.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.14.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/klauspost/compress v1.17.4 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/mailru/easyjson v0.7.7
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/pelletier/go-toml/v2 v2.0.8 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/ugorji/go/codec v1.2.11 // indirect
	go.uber.org/automaxprocs v1.5.3 // indirect
	golang.org/x/sync v0.8.0 // indirect
	google.golang.org/protobuf v1.35.1
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
	gopkg.in/yaml.v2 v2.4.0 // indirect
)

replace (
	github.com/googleapis/google-cloud-go/storage => cloud.google.com/go/storage v1.30.1
	github.com/prometheus/client_golang => github.com/flashcatcloud/client_golang v1.12.2-0.20220704074148-3b31f0c90903
	go.opentelemetry.io/collector => github.com/open-telemetry/opentelemetry-collector v0.54.0
	google.golang.org/genproto => google.golang.org/genproto v0.0.0-20240604185151-ef581f913117
)
