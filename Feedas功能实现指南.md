# Feedas Simulation 日志采集功能实现指南

## 需求分析

### 核心需求
- 新增feedas平台的simulation日志采集功能
- 支持pb日志格式解析
- 使用codex工具进行protobuf解析
- 为后续模块扩展打基础（类似采集方式）

### 技术要求
- 下载并解析protobuf协议文件
- 支持SequenceFile格式的pb日志
- 集成到现有的log-agent架构中

## 实现方案分析

### 1. 为什么这样设计？

#### 1.1 现有架构优势
- **模块化设计**：项目已有完善的平台扩展机制
- **Protobuf支持**：已有`protoloader`包提供proto解析能力
- **配置驱动**：通过配置文件灵活控制日志采集行为
- **处理管道**：成熟的 `输入→解码→处理→发送` 流程

#### 1.2 为什么选择这种实现方式？
- **最小侵入性**：复用现有的proto解析框架
- **一致性**：与DAS、ASP等现有平台保持相同的扩展模式
- **可扩展性**：为后续类似模块提供标准化的实现路径

### 2. 重点关注的代码区域

#### 2.1 核心文件和目录
```
关键路径：
├── logs/platform/source.go          # 平台定义 ⭐️
├── config/logs/integration_config.go # 配置结构 ⭐️  
├── config/config.go                 # 配置初始化 ⭐️
├── logs/protocol/protoloader/       # Proto解析器 ⭐️
├── logs/protocol/head/              # 协议头解析 (参考)
└── conf/logs.toml                   # 配置文件 ⭐️
```

#### 2.2 关键接口和结构体
- `Config` 结构体：`config/logs/integration_config.go:47`
- `ProtoParser` 接口：`logs/protocol/protoloader/proto.go:27`
- 平台常量定义：`logs/platform/source.go:5-21`
- `CustormLogParse()` 函数：`logs/platform/source.go:24`

### 3. 实现步骤详解

#### 3.1 第一步：添加平台支持 🚀
**修改文件**: `logs/platform/source.go`

**为什么要改这里？**
- 这是所有平台的统一注册中心
- 需要将feedas加入支持的平台列表
- 需要标识feedas为需要自定义解析的平台

**怎么改？**
```go
const (
    // 现有平台...
    FEEDAS = "feedas"  // 新增
)

func CustormLogParse(source string) bool {
    switch source {
    case DAS, FlowDas, ASP, ALS, FEEDAS:  // 添加FEEDAS
        return true
    }
    return false
}

func ValidCustomPlat(source string) error {
    switch source {
    case EBT, NBCORE, NBSERVER, NXSTP, CARDAN, EOS,
         FlowCloud, FlowCloudServer, DAS, FlowDas, ASP, ALS, FEEDAS:  // 添加FEEDAS
        return nil
    }
    return errors.New("invalid source")
}
```

#### 3.2 第二步：创建feedas协议解析器 🔧
**新建文件**: `logs/protocol/head/feedas.go`

**为什么要新建这个文件？**
- 参考DAS(`logs/protocol/head/das.go`)和ASP(`logs/protocol/head/asp.go`)的实现
- feedas有特殊的SequenceFile格式，需要专门的解析逻辑
- 保持与现有架构的一致性

**实现什么功能？**
```go
package protocol

// SequenceFileHeader 定义SequenceFile格式的头部结构
type SequenceFileHeader struct {
    // 根据SequenceFile格式定义具体字段
    KeyLength   uint32
    ValueLength uint32
    // 其他必要字段...
}

// FeedasHeader feedas日志头解析器
type FeedasHeader struct {
    Header   *SequenceFileHeader
    isParsed bool
}

// 实现必要的接口方法：
// - ParseByFile(b *bufio.Reader) error
// - BodyLength() int  
// - ToPlainData() map[string]any
// - IsParsed() bool
// - ReadBodyBuf(src *bufio.Reader, dst *bytes.Buffer) error
// - Size() int64
// - GetMeta() (any, error)
```

#### 3.3 第三步：配置proto文件支持 📝
**修改文件**: `conf/logs.toml`

**为什么要修改配置？**
- 让系统知道如何找到和解析feedas的proto文件
- 指定具体的消息类型进行解析

**怎么配置？**
```toml
[[logs.items]]
type = "file"
path = "/path/to/simulation_pblog.pb.log"  # 实际的pb日志路径
source = "feedas"                          # 新增的平台标识
proto_path = "./proto"                     # proto文件目录
proto_file = "simulation_pblog.proto"      # proto文件名
message = "baidu.feedas.SimulationPblog"   # 消息类型
```

#### 3.4 第四步：集成到主流程 🔗
**修改文件**: 根据DAS/ASP的集成方式，可能需要修改：
- `logs/protocol/head/head.go` - 添加feedas头解析器工厂
- `logs/decoder/decoder.go` - 可能需要特殊的解码逻辑

**为什么要集成？**
- 让主程序知道如何处理feedas类型的日志
- 确保解析器能被正确调用

### 4. 开发和测试流程

#### 4.1 准备工作
```bash
# 1. 下载proto文件
mkdir -p proto
wget hdc.baidu-int.com:9119/home/<USER>/ecom_im/nativeqa/proto_release/baidu/native-ads/feedas/proto.tar
tar -xf proto.tar

# 2. 下载测试日志
wget hdc.baidu-int.com:9119/home/<USER>/ecom_im/nativeqa/proto_release/baidu/native-ads/feedas/simulation_pblog.pb.log

# 3. 验证codex解析（参考用法）
codex --file simulation_pblog.pb.log --proto=./proto/simulation_pblog.proto --message=baidu.feedas.SimulationPblog --type=SequenceFile
```

#### 4.2 开发调试
```bash
# 1. 开发模式运行
go run main.go --source=feedas --debug --test --configs=./conf

# 2. 验证配置加载
# 检查日志输出是否正确识别feedas平台和proto配置

# 3. 测试proto解析
# 确认protobuf文件被正确加载和解析
```

#### 4.3 功能验证
1. **配置验证**：确认feedas被识别为有效平台
2. **Proto加载**：验证proto文件正确加载
3. **日志解析**：确认pb日志能够被正确解析为JSON
4. **数据传输**：验证解析后的数据能正确发送到kafka

### 5. 注意事项和最佳实践

#### 5.1 开发注意事项
- **错误处理**：参考DAS/ASP的错误处理方式
- **内存管理**：使用`util.DefaultBytesPool`进行缓冲区管理
- **日志记录**：添加适当的调试日志便于排查问题
- **配置验证**：确保proto_path、proto_file、message都正确配置

#### 5.2 性能考虑
- **缓冲区复用**：使用现有的bytes pool机制
- **并发处理**：利用现有的pipeline并发机制
- **内存占用**：注意大文件处理时的内存使用

#### 5.3 扩展性设计
- **接口统一**：为后续类似模块提供统一的实现模式
- **配置标准化**：建立标准的proto解析配置格式
- **代码复用**：抽象共同的pb日志处理逻辑

### 6. 潜在问题和解决方案

#### 6.1 SequenceFile格式解析
**问题**：SequenceFile是Hadoop特有格式，可能需要特殊处理
**解决**：参考codex工具的解析方式，实现对应的头部解析逻辑

#### 6.2 Proto文件依赖
**问题**：proto文件可能有复杂的依赖关系
**解决**：确保proto_path包含所有依赖的proto文件目录

#### 6.3 消息类型识别
**问题**：如何正确识别和解析不同的消息类型
**解决**：通过配置中的message字段明确指定消息类型

### 7. 验收标准

#### 7.1 功能完整性验收
- ✅ feedas平台被系统正确识别
- ✅ proto文件被正确加载和解析
- ✅ pb日志文件能够被正确读取
- ✅ 二进制日志被正确解析为JSON格式
- ✅ 解析后的数据能正确发送到kafka

#### 7.2 配置灵活性验收
- ✅ 支持通过配置文件指定proto路径和文件
- ✅ 支持指定具体的消息类型
- ✅ 配置错误时有明确的错误提示

#### 7.3 性能稳定性验收
- ✅ 大文件处理不会导致内存溢出
- ✅ 错误日志不会影响系统稳定性
- ✅ 支持与其他平台并行运行

## 总结

这个实现方案的核心思路是**"站在巨人肩膀上"**，充分复用现有的架构和代码：

1. **平台扩展**：复用platform包的扩展机制
2. **Proto解析**：复用protoloader的解析能力  
3. **协议处理**：参考DAS/ASP的实现模式
4. **配置管理**：复用现有的配置体系

通过这种方式，可以用最小的代码改动实现feedas支持，同时为后续类似模块的扩展建立标准化的实现路径。

关键成功因素：
- 理解现有架构，复用而不重造
- 参考类似实现，保持一致性
- 充分测试，确保稳定性
- 文档完善，便于后续扩展