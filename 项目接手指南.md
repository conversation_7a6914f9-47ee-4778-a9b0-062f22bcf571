# Log-Agent 项目接手指南

## 项目概述

**log-agent** 是一个基于Go语言开发的日志收集代理系统，主要用于企业级日志收集、处理和传输。该项目基于Datadog logs-agent进行定制开发，专门服务于百度内部的ep-qa团队。

### 核心功能
- 🔄 多源日志采集（文件、系统日志、网络监听等）
- 📡 多种传输协议支持（HTTP、TCP、Kafka）
- 🔍 实时日志处理和过滤
- 🏷️ 自动标签添加和主机信息识别
- 📊 支持protobuf等格式解析
- 🔧 支持多平台部署（nb-core、nb-server、ebt、cardan等）

## 项目架构

### 整体架构
```
输入源 → 收集器 → 解码器 → 处理器 → 发送器 → 审计器
```

### 核心模块

1. **Agent模块** (`agent/`)
   - 主要入口点：`main.go:109`
   - 日志代理核心：`agent/logs_agent.go`
   - 模块管理：`agent/agent.go`

2. **日志处理管道** (`logs/`)
   - 管道提供者：`logs/pipeline/provider.go`
   - 核心管道：`logs/pipeline/pipeline.go`
   - 消息处理：`logs/processor/`

3. **输入源** (`logs/input/`)
   - 文件监控：`logs/input/file/`
   - 网络监听：`logs/input/listener/`
   - 系统日志：`logs/input/journald/`
   - 容器日志：`logs/input/container/`

4. **客户端/传输** (`logs/client/`)
   - HTTP传输：`logs/client/http/`
   - TCP传输：`logs/client/tcp/`
   - Kafka传输：`logs/client/kafka/`

5. **配置管理** (`config/`)
   - 主配置：`config/config.go`
   - 日志配置：`config/logs/`

## 关键路径和文件

### 启动流程关键路径
1. **程序入口**：`main.go:78` → `main()`
2. **配置初始化**：`main.go:99` → `config.InitConfig()`
3. **Agent创建**：`main.go:109` → `agent.NewAgent()`
4. **Agent启动**：`main.go:114` → `runAgent(ag)`
5. **日志处理启动**：`agent/logs_agent.go:59` → `NewLogsAgent()`

### 核心配置文件
- `conf/config.toml` - 全局配置
- `conf/logs.toml` - 日志收集配置

### 重要接口和结构体
- `agent.Module` - 代理模块接口（`agent/agent.go:15`）
- `LogsAgent` - 日志代理主结构体（`agent/logs_agent.go:46`）
- `Pipeline` - 处理管道结构体（`logs/pipeline/pipeline.go:26`）

## 快速上手指南

### 1. 环境准备
- Go 1.23.8+
- 了解TOML配置格式
- 熟悉Kafka/HTTP协议

### 2. 配置理解
编辑 `conf/logs.toml`：
```toml
[logs]
enable = true                    # 启用日志收集
send_to = "************:8292"   # 发送目标
send_type = "kafka"             # 传输协议
topic = "auto"                  # 主题（自动根据source设置）

[[logs.items]]
type = "file"                   # 收集类型
path = "/home/<USER>/test/*"      # 文件路径
source = "auto"                 # 来源标识（自动根据启动参数设置）
```

### 3. 运行和部署
```bash
# 开发运行
go run main.go --source=nb-server --configs=./conf

# 构建
go build -o log-agent main.go

# 服务安装
./log-agent --install

# 服务启动
./log-agent --start
```

### 4. 支持的平台和来源
- `nb-core` - nb核心平台
- `nb-server` - nb服务平台  
- `ebt` - ebt平台
- `cardan` - cardan平台
- `flowcloud` - flowcloud平台
- `flowcloudserver` - flowcloud服务平台

## 开发调试指南

### 1. 日志调试
```bash
# 开启调试模式
./log-agent --debug --debug-level=2 --configs=./conf

# 测试模式（输出到stdout）
./log-agent --test --configs=./conf
```

### 2. 关键调试点
- 配置加载：`config/config.go:87`
- Agent启动：`agent/agent.go:34`
- 管道处理：`logs/pipeline/pipeline.go:82`
- 消息发送：`logs/sender/sender.go`

### 3. 性能监控
- HTTP接口：默认`:9100`端口
- pprof性能分析：发送`SIGUSR2`信号

## 常见问题和解决方案

### 1. 配置问题
- **问题**：日志不采集
- **检查**：`logs.enable=true`，路径权限，文件存在性

### 2. 连接问题  
- **问题**：无法发送到Kafka/HTTP
- **检查**：网络连通性，认证配置，端口开放

### 3. 性能问题
- **调整**：`chan_size`、`pipeline`数量、`batch_max_size`

### 4. 协议解析问题
- **DAS/ASP模块**：需要配置`proto_path`和`proto_file`
- **多行日志**：配置`processing_rules`

## 代码贡献指南

### 1. 代码风格
- 遵循Go标准代码风格
- 使用gofmt格式化代码
- 添加适当的注释和文档

### 2. 测试
- 单元测试：使用Go标准testing包
- 集成测试：确保端到端功能正常

### 3. 提交规范
- 清晰的commit message
- 功能完整的PR
- 必要的文档更新

## 联系和支持

- **代码仓库**：icode.baidu.com/baidu/ep-qa/log-agent
- **团队**：ep-qa团队
- **问题反馈**：通过内部issue系统

---

**重要提醒**：
- 在生产环境部署前，务必在测试环境充分验证
- 关注日志文件大小和轮转配置，避免磁盘空间不足
- 定期检查系统资源使用情况，适时调整并发参数