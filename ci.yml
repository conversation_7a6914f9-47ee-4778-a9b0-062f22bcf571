# 百度 Go 编译环境使用指南 https://ku.baidu-int.com/d/SzGt0sD37hWmmp

Global:
    version: 2.0
    group_email: yang<PERSON><PERSON>@baidu.com   # <------ 配置团队邮箱地址，用于接收xx.latest软件版本升级通知邮件


Default:
    profile : [build]

Profiles:
    - profile:
      name : build
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
          - go: 1.23.latest
          - gcc: 12
      build:
        command: make -f Makefile
        cache:                    # http://buildcloud.baidu.com/bcloud/9-bcloud_subcmd#9.15-cache
          enable: true            # 删除模块全部缓存：bcloud cache --delete --repo=baidu/gdp/ghttp
          trimeThresholdSize: 3
#          paths:                 # paths 为空时，会使用编译系统默认的缓存目录
#            - cache
      check:
        - reuse: TASK
          enable: true
      artifacts:
        release: true
