//go:build !no_logs

// Unless explicitly stated otherwise all files in this repository are licensed
// under the Apache License Version 2.0.
// This product includes software developed at Datadog (https://www.datadoghq.com/).
// Copyright 2016-present Datadog, Inc.

package processor

import (
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/ep-qa/log-agent/config"
	"icode.baidu.com/baidu/ep-qa/log-agent/logs/message"
)

const nanoToMillis = 1000000

// JSONEncoder is a shared json encoder.
var JSONEncoder Encoder = &jsonEncoder{}

// jsonEncoder transforms a message into a JSON byte array.
type jsonEncoder struct{}

// JSON representation of a message.
type jsonPayload struct {
	Message   string `json:"message"` // log message
	Status    string `json:"status"`  // log level
	Timestamp int64  `json:"timestamp"`
	Hostname  string `json:"agent_hostname"`
	Source    string `json:"source"`
	Env       string `json:"env"`
	Service   string `json:"service"`
	Task      string `json:"task"`
	LogFile   string `json:"log_file"`
	Tags      string `json:"tags"`
}

// Encode encodes a message into a JSON byte array.
func (j *jsonEncoder) Encode(msg *message.Message, redactedMsg []byte) ([]byte, error) {
	ts := time.Now().UTC()
	if !msg.Timestamp.IsZero() {
		ts = msg.Timestamp
	}
	accuracy := config.Config.Logs.Accuracy
	if msg.Origin.LogSource.Config.Accuracy != "" {
		accuracy = msg.Origin.LogSource.Config.Accuracy
	}
	if accuracy == "" {
		accuracy = "ms"
	}
	timestamp := ts.UnixMilli()
	switch accuracy {
	case "s":
		timestamp = timestamp / 1000
	case "m":
		timestamp = timestamp / 60000
	}
	// topic := config.Config.Logs.Topic
	// if msg.Origin.LogSource.Config.Topic != "" {
	// 	topic = msg.Origin.LogSource.Config.Topic
	// }
	// msgKey := config.Config.Logs.APIKey
	// if config.Config.Logs.SendType == "kafka" {
	// 	msgKey = msg.GetHostname() + "/" + msg.Origin.GetIdentifier()
	// }

	return json.Marshal(jsonPayload{
		Message:   toValidUtf8(redactedMsg),
		Status:    msg.GetStatus(),
		Timestamp: timestamp,
		Hostname:  msg.GetHostname(),
		Env:       msg.Origin.Env(),
		LogFile:   msg.Origin.LogFile(),
		Service:   msg.Origin.Service(),
		Source:    msg.Origin.Source(),
		Task:      msg.Origin.Task(),
		Tags:      msg.Origin.TagsToJSONString(),
		// Topic:     topic,
		// MsgKey:    msgKey,
	})
}
