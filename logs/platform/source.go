package platform

import "errors"

const (
	EOS             = "eos"
	EBT             = "ebt"
	NBCORE          = "nb-core"
	NBSERVER        = "nb-server"
	CARDAN          = "cardan"
	NXSTP           = "nxstp"
	FlowCloud       = "flowcloud"
	FlowCloudServer = "flowcloudserver"
	DAS             = "das"
	FlowDas         = "flowdas"
	ASP             = "asp"
	ALS             = "als"
	ONLINE          = "online"
	OFFLINE         = "offline"
	GREY            = "grey"
	FEEDAS          = "feedas"
)

// 商业日志存储是二进制 需要按协议进行解析为明文再上报
func CustormLogParse(source string) bool {
	switch source {
	case DAS, FlowDas, ASP, ALS, FEEDAS:
		return true
	}
	return false
}

func ValidCustomPlat(source string) error {
	switch source {
	case EBT, NBCORE, NBSERVER, NXSTP, CARDAN, EOS,
		FlowCloud, FlowCloudServer, DAS, FlowDas, ASP,
		ALS, FEEDAS:
		return nil
	}
	return errors.New("invalid source")
}
