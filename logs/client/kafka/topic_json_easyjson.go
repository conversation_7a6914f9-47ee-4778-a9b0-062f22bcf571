//go:build !no_logs

// Code generated by easyjson for marshaling/unmarshaling. DO NOT EDIT.

package kafka

import (
	json "encoding/json"
	easyjson "github.com/mailru/easyjson"
	jlexer "github.com/mailru/easyjson/jlexer"
	jwriter "github.com/mailru/easyjson/jwriter"
)

// suppress unused package warning
var (
	_ *json.RawMessage
	_ *jlexer.Lexer
	_ *jwriter.Writer
	_ easyjson.Marshaler
)

func easyjsonDe48e4d6DecodeFlashcatCloudCategrafLogsClientKafka(in *jlexer.Lexer, out *Data) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "topic":
			out.Topic = string(in.String())
		case "msg_key":
			out.MsgKey = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonDe48e4d6EncodeFlashcatCloudCategrafLogsClientKafka(out *jwriter.Writer, in Data) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"topic\":"
		out.RawString(prefix[1:])
		out.String(string(in.Topic))
		const prefix1 string = ",\"msg_key\":"
		out.RawString(prefix1[1:])
		out.String(string(in.MsgKey))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Data) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonDe48e4d6EncodeFlashcatCloudCategrafLogsClientKafka(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Data) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonDe48e4d6EncodeFlashcatCloudCategrafLogsClientKafka(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Data) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonDe48e4d6DecodeFlashcatCloudCategrafLogsClientKafka(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Data) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonDe48e4d6DecodeFlashcatCloudCategrafLogsClientKafka(l, v)
}
