//go:build !no_logs

package kafka

import (
	"fmt"

	"github.com/IBM/sarama"
)

type MessageBuilder struct {
	sarama.ProducerMessage
}

func NewBuilder() *MessageBuilder {
	return &MessageBuilder{}
}

func (m *MessageBuilder) WithMessage(key string, value []byte) *MessageBuilder {
	m.Key = sarama.StringEncoder(key)
	m.Value = sarama.ByteEncoder(value)
	return m
}

func (m *MessageBuilder) WithTopic(topic string) *MessageBuilder {
	m.Topic = topic
	return m
}

func (m *MessageBuilder) build() (*sarama.ProducerMessage, error) {
	switch {
	case len(m.Topic) == 0:
		return nil, fmt.Errorf("message (%s) must not be nil", "topic")
	case m.Key.Length() == 0:
		return nil, fmt.Errorf("message (%s) must not be nil", "key")
	case m.Value.Length() == 0:
		return nil, fmt.E<PERSON><PERSON>("message (%s) must not be nil", "value")
	}
	return &m.ProducerMessage, nil
}

func (m *MessageBuilder) Send(producer Producer) error {
	if producer == nil {
		return fmt.<PERSON><PERSON><PERSON>("empty producer")
	}

	msg, err := m.build()
	if err != nil {
		return err
	}

	return producer.Send(msg)
}
