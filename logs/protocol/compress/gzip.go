package compress

import (
	"bytes"
	"compress/gzip"
	"io"
	"sync"
)

var (
	spWriter sync.Pool
	spReader sync.Pool
)

func init() {
	spWriter = sync.Pool{New: func() interface{} {
		return gzip.NewWriter(nil)
	}}
	spReader = sync.Pool{New: func() interface{} {
		return new(gzip.Reader)
	}}
}

type Gzip struct {
}

func (g Gzip) Compress(data []byte) ([]byte, error) {
	buf := bytes.NewBuffer(make([]byte, 0, len(data)))
	w := spWriter.Get().(*gzip.Writer)
	w.Reset(buf)

	defer func() {
		w.Close()
		spWriter.Put(w)
	}()
	_, err := w.Write(data)
	if err != nil {
		return nil, err
	}
	err = w.Flush()
	if err != nil {
		return nil, err
	}
	err = w.Close()
	if err != nil {
		return nil, err
	}
	dec := buf.Bytes()
	return dec, nil
}

func (g Gzip) UnCompress(data []byte) ([]byte, error) {
	buf := bytes.NewBuffer(data)

	gr := spReader.Get().(*gzip.Reader)
	defer func() {
		spReader.Put(gr)
	}()
	err := gr.Reset(buf)
	if err != nil {
		return nil, err
	}
	defer gr.Close()

	data, err = io.ReadAll(gr)
	if err != nil {
		return nil, err
	}
	return data, err
}
