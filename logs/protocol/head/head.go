package protocol

import (
	"bufio"
	"bytes"

	"icode.baidu.com/baidu/ep-qa/log-agent/logs/platform"
)

type Header interface {
	ParseByFile(b *bufio.Reader) error                      // 分块读取
	ReadBodyBuf(src *bufio.Reader, dst *bytes.Buffer) error // get body buf
	ToPlainData() map[string]any                            // parse to map
	GetMeta() (any, error)
	Size() int64
	BodyLength() int
}

func NewHeader(source string) Header {
	switch source {
	case platform.DAS:
		return NewPackHeader(false)
	case platform.FlowDas:
		return NewPackHeader(true)
	case platform.ASP, platform.ALS:
		return NewASPHeader()
	}
	return nil
}
