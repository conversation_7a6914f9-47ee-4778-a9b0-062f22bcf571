package protocol

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"

	"icode.baidu.com/baidu/ep-qa/log-agent/logs/protocol/compress"
	"icode.baidu.com/baidu/ep-qa/log-agent/logs/util"
)

var ASPSize int64 = 24

/*
	typedef struct _loghead_t {
		uint32_t magic;		// magic number
		uint32_t version;	// 协议版本
		uint32_t uncompress_len;// 日志压缩前长度
		uint32_t compress_len;	// 日志压缩后长度
	} loghead_t;
*/

type Loghead struct {
	MagicNumber   uint32
	Version       uint32
	UncompressLen uint32
	CompressLen   uint32
	TimeStamp     uint64 `json:"timestamp"`
}

type ASPHeader struct {
	Loghead      *Loghead
	isParsed     bool
	needCompress bool
}

func parseASPHeader(reader *bytes.Buffer) (*Loghead, error) {
	var header Loghead

	// Parse the basic fields
	err := binary.Read(reader, binary.LittleEndian, &header.MagicNumber)
	if err != nil {
		return nil, err
	}
	if header.MagicNumber != 0xB0AEBEA7 {
		return nil, fmt.Errorf("magic number err, cur %v", header.MagicNumber)
	}
	err = binary.Read(reader, binary.LittleEndian, &header.Version)
	if err != nil {
		return nil, err
	}
	err = binary.Read(reader, binary.LittleEndian, &header.UncompressLen)
	if err != nil {
		return nil, err
	}
	err = binary.Read(reader, binary.LittleEndian, &header.CompressLen)
	if err != nil {
		return nil, err
	}
	err = binary.Read(reader, binary.LittleEndian, &header.TimeStamp)
	if err != nil {
		return nil, err
	}
	return &header, nil
}

func NewASPHeader() *ASPHeader {
	return &ASPHeader{}
}

func (i *ASPHeader) ParseByFile(b *bufio.Reader) error {
	return i.parse(b)
}

func (i *ASPHeader) parse(rd io.Reader) error {
	buf := util.DefaultBytesPool.Get()
	defer util.DefaultBytesPool.Put(buf)

	if n, err := io.CopyN(buf, rd, ASPSize); err != nil || n != int64(ASPSize) {
		if n == 0 {
			return ErrEOF
		}
		return fmt.Errorf("read pack head buf error err:%w actual read %d", err, n)
	}
	ph, err := parseASPHeader(buf)
	if err != nil {
		return err
	}
	i.Loghead = ph
	i.isParsed = true
	i.needCompress = i.Loghead.CompressLen > 0
	return nil
}

func (i *ASPHeader) BodyLength() int {
	if i.Loghead.UncompressLen != 0 {
		return int(i.Loghead.UncompressLen)
	}
	return int(i.Loghead.CompressLen)
}

func (i *ASPHeader) ToPlainData() map[string]any {
	res, err := json.Marshal(i.Loghead)
	if err != nil {
		return nil
	}
	var hash map[string]any
	err = json.Unmarshal(res, &hash)
	if err != nil {
		return nil
	}
	return hash
}

func (i *ASPHeader) IsParsed() bool {
	return i.isParsed
}

func (i *ASPHeader) ReadBodyBuf(src *bufio.Reader, dst *bytes.Buffer) error {
	bodyLen := int64(i.BodyLength())
	if _, err := io.CopyN(dst, src, bodyLen); err != nil {
		return fmt.Errorf("read body buf error: %w, expected size: %d", err, bodyLen)
	}

	if i.needCompress {
		uncompressed, err := compress.Gzip{}.UnCompress(dst.Bytes())
		if err != nil {
			return fmt.Errorf("uncompress error: %w", err)
		}

		dst.Reset()
		if _, err := dst.Write(uncompressed); err != nil {
			return fmt.Errorf("failed to write uncompressed data: %w", err)
		}
	}

	return nil
}

func (i *ASPHeader) Size() int64 {
	return ASPSize
}

func (i *ASPHeader) GetMeta() (any, error) {
	return nil, nil
}
