package protocol

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"io"

	"icode.baidu.com/baidu/ep-qa/log-agent/logs/protocol/protoloader"
	"icode.baidu.com/baidu/ep-qa/log-agent/logs/util"
)

var DASSize int64 = 48
var ErrEOF error = errors.New("EOF")

type PackH struct {
	MagicNumber      uint16
	Checksum         uint16
	PackLen          uint32
	Source           uint8
	Level            uint16 // 10 bits used, but stored in 16 bits for alignment
	Reserve1         uint16 // 13 bits used, but stored in 16 bits for alignment
	Extend           uint8  // 1 bit used, but stored in 8 bits for simplicity
	HeartBeat        uint8  // 1 bit used, packed in 8 bits
	OpType           uint8  // 2 bits used, packed in 8 bits
	SchemaChangeFlag uint8  // 1 bit used, packed in 8 bits
	Reserve2         uint32 // 28 bits used, but stored in 32 bits for alignment
	TimeStamp        uint64 `json:"time_stamp"`
	EventID          uint64
	KeyID            uint64
	Reserve3         uint64
}

type PackHeader struct {
	Pack     *PackH
	isParsed bool
	nestedpb bool // 除fc.proto外 其他都需按pb的序列查找message名称
}

func parsePackHeader(reader *bytes.Buffer) (*PackH, error) {
	var header PackH

	// Parse the basic fields
	err := binary.Read(reader, binary.LittleEndian, &header.MagicNumber)
	if err != nil {
		return nil, err
	}
	if header.MagicNumber != 0xf83f {
		return nil, fmt.Errorf("magic number err, cur %v", header.MagicNumber)
	}
	err = binary.Read(reader, binary.LittleEndian, &header.Checksum)
	if err != nil {
		return nil, err
	}
	err = binary.Read(reader, binary.LittleEndian, &header.PackLen)
	if err != nil {
		return nil, err
	}

	// Parse bit fields
	var bitFields1 uint32
	err = binary.Read(reader, binary.LittleEndian, &bitFields1)
	if err != nil {
		return nil, err
	}
	header.Source = uint8(bitFields1 & 0xFF)
	header.Level = uint16((bitFields1 >> 8) & 0x3FF)
	header.Reserve1 = uint16((bitFields1 >> 18) & 0x1FFF)
	header.Extend = uint8((bitFields1 >> 31) & 0x1)

	if header.Extend == 1 {
		var bitFields2 uint32
		err = binary.Read(reader, binary.LittleEndian, &bitFields2)
		if err != nil {
			return nil, err
		}
		header.HeartBeat = uint8(bitFields2 & 0x1)
		header.OpType = uint8((bitFields2 >> 1) & 0x3)
		header.SchemaChangeFlag = uint8((bitFields2 >> 3) & 0x1)
		header.Reserve2 = uint32(bitFields2 >> 4)
	}

	// Parse the rest of the fields
	err = binary.Read(reader, binary.LittleEndian, &header.TimeStamp)
	if err != nil {
		return nil, err
	}
	err = binary.Read(reader, binary.LittleEndian, &header.EventID)
	if err != nil {
		return nil, err
	}
	err = binary.Read(reader, binary.LittleEndian, &header.KeyID)
	if err != nil {
		return nil, err
	}
	err = binary.Read(reader, binary.LittleEndian, &header.Reserve3)
	if err != nil {
		return nil, err
	}

	return &header, nil
}

func NewPackHeader(nestedpb bool) *PackHeader {
	return &PackHeader{nestedpb: nestedpb}
}

func (i *PackHeader) ParseByFile(b *bufio.Reader) error {
	return i.parse(b)
}

func (i *PackHeader) parse(rd io.Reader) error {
	buf := util.DefaultBytesPool.Get()
	defer util.DefaultBytesPool.Put(buf)

	if n, err := io.CopyN(buf, rd, DASSize); err != nil || n != int64(48) {
		if n == 0 {
			return ErrEOF
		}
		return fmt.Errorf("read pack head buf error err:%w actual read %d", err, n)
	}
	ph, err := parsePackHeader(buf)
	if err != nil {
		return err
	}
	i.Pack = ph
	i.isParsed = true
	return nil
}

func (i *PackHeader) BodyLength() int {
	return int(i.Pack.PackLen)
}

func (i *PackHeader) ToPlainData() map[string]any {
	res, err := json.Marshal(i.Pack)
	if err != nil {
		return nil
	}
	var hash map[string]any
	err = json.Unmarshal(res, &hash)
	if err != nil {
		return nil
	}
	return hash
}

func (i *PackHeader) IsParsed() bool {
	return i.isParsed
}

func (i *PackHeader) ReadBodyBuf(src *bufio.Reader, dst *bytes.Buffer) error {
	bodyLen := i.BodyLength()
	if n, err := io.CopyN(dst, src, int64(bodyLen)); err != nil || n != int64(bodyLen) {
		return fmt.Errorf("read body buf error %w need read size:%d actual read %d", err, bodyLen, n)
	}
	return nil
}

func (i *PackHeader) Size() int64 {
	return DASSize
}

func (i *PackHeader) GetMeta() (any, error) {
	if i.nestedpb {
		majorPb, err := protoloader.GolbalProtoParser.GetMessage("Flow")
		if err != nil {
			return nil, err
		}
		f := majorPb.FindFieldByNumber(int32(i.Pack.Level))
		if f == nil {
			return "heartbeat", nil
		}
		return f.GetName(), nil
	}
	return fmt.Sprintf("level%d", i.Pack.Level), nil
}
