package body

import (
	"encoding/json"
	"errors"

	"github.com/golang/protobuf/jsonpb"
	"icode.baidu.com/baidu/ep-qa/log-agent/logs/protocol/protoloader"
	"icode.baidu.com/baidu/ep-qa/log-agent/pkg/proto/dynamic"
)

type Protobuf struct {
	parserRes      map[string]any
	custormMessage string
}

func NewProtobuf() *Protobuf {
	return &Protobuf{parserRes: make(map[string]any, 8)}
}

// 设置header meta message信息
func (p *Protobuf) AddHeaderMeta(meta any) error {
	l, ok := meta.(string)
	if ok {
		p.custormMessage = l
		return nil
	}
	return nil
}

func (p *Protobuf) Parse(buf []byte) (err error) {
	if len(buf) == 0 {
		return errors.New("protobuf is nil")
	}
	msg, err := protoloader.GolbalProtoParser.GetMessage(p.custormMessage)
	if err != nil {
		return err
	}
	dMsg := dynamic.NewMessage(msg)
	_ = dMsg.Unmarshal(buf)
	jsonD, err := dMsg.MarshalJSONPB(&jsonpb.Marshaler{OrigName: true})
	if err != nil {
		return err
	}
	p.parserRes["Message"] = msg.GetName()
	return json.Unmarshal(jsonD, &p.parserRes)
}

func (p *Protobuf) ToPlainData() (map[string]any, error) {
	return p.parserRes, nil
}
