package body

import (
	"icode.baidu.com/baidu/ep-qa/log-agent/logs/platform"
)

type Body interface {
	Parse(buf []byte) error
	ToPlainData() (map[string]any, error) // return map[string]any,size,error if param1 !=map[string]any ,size = 0
	AddHeaderMeta(input any) error        // 补充信息
}

func NewBody(source string) Body {
	switch source {
	case platform.DAS, platform.FlowDas, platform.ASP, platform.ALS:
		return NewProtobuf()
	}
	return nil
}
