package protoloader

import (
	"fmt"
	"os"
	"strings"

	"github.com/jhump/protoreflect/desc"
	"github.com/jhump/protoreflect/desc/protoparse"
)

var (
	GolbalProtoParser *ProtoParser
)

// TODO: 支持多proto
func InitProtoFile(path, file, message []string) *ProtoParser {
	var protoPaths, protoFiles, messages []string

	protoPaths = append(protoPaths, path...)
	protoFiles = append(protoFiles, file...)
	messages = append(messages, message...)

	return NewProtoFile(protoPaths, protoFiles, messages)
}

type ProtoParser struct {
	file, message     []string
	inputMessagePool  map[string]string
	outputMessagePool map[string]string

	Descriptor []*desc.FileDescriptor
}

func NewProtoFile(protoPaths, protoFiles, messages []string) *ProtoParser {
	if len(protoPaths) == 0 || len(protoFiles) == 0 || len(messages) == 0 {
		return nil
	}
	var parser protoparse.Parser
	parser.ImportPaths = protoPaths
	protoP := ProtoParser{
		file:              protoFiles,
		message:           messages,
		inputMessagePool:  make(map[string]string, 8),
		outputMessagePool: make(map[string]string, 8),
	}
	descriptors, err := parser.ParseFiles(protoFiles...)
	if err != nil {
		fmt.Printf("ParseFiles proto file %s failed. err:%s", protoFiles, err.Error())
		os.Exit(1)
	}
	protoP.Descriptor = descriptors
	GolbalProtoParser = &protoP
	return &protoP
}

func (p *ProtoParser) GetMessage(m ...string) (*desc.MessageDescriptor, error) {
	var message string
	if len(m) == 1 {
		message = m[0]
	}

	for i := range p.file {
		if message == "" {
			message = p.message[i]
		}
		if res := strings.Split(message, "."); len(res) == 1 && p.Descriptor[i].GetPackage() != "" {
			message = p.Descriptor[i].GetPackage() + "." + message
		}
		if res := p.Descriptor[i].FindMessage(message); res != nil {
			return res, nil
		}
	}
	return nil, fmt.Errorf("can not find message %s ", message)
}
