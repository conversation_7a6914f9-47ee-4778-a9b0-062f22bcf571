package api

import (
	"crypto/tls"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/ep-qa/log-agent/config"
)

func Start() {
	if config.Config == nil ||
		config.Config.HTTP == nil ||
		!config.Config.HTTP.Enable {
		return
	}
	// test mode 不开启
	if config.Config.TestMode {
		return
	}

	conf := config.Config.HTTP

	gin.SetMode(conf.RunMode)

	r := gin.New()

	configRoutes(r)

	srv := &http.Server{
		Addr:         conf.Address,
		Handler:      r,
		ReadTimeout:  time.Duration(conf.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(conf.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(conf.IdleTimeout) * time.Second,
	}

	log.Println("I! http server listening on:", conf.Address)

	var err error
	if conf.CertFile != "" && conf.KeyFile != "" {
		srv.TLSConfig = &tls.Config{MinVersion: tls.VersionTLS12}
		err = srv.ListenAndServeTLS(conf.CertFile, conf.KeyFile)
	} else {
		err = srv.ListenAndServe()
	}

	if err != nil && err != http.ErrServerClosed {
		panic(err)
	}
}

func configRoutes(r *gin.Engine) {
	r.GET("/ping", func(c *gin.Context) {
		c.String(200, "pong")
	})
}
