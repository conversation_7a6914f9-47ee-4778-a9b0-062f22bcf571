//nolint:all
package config

import (
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"path"
	"runtime"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/toolkits/pkg/file"
	"icode.baidu.com/baidu/ep-qa/log-agent/config/logs"
	"icode.baidu.com/baidu/ep-qa/log-agent/logs/platform"
	"icode.baidu.com/baidu/ep-qa/log-agent/logs/protocol/protoloader"
	"icode.baidu.com/baidu/ep-qa/log-agent/pkg/cfg"
)

const (
	defaultProbeAddr = "*********:80"
)

var envVarEscaper = strings.NewReplacer(
	`"`, `\"`,
	`\`, `\\`,
)

type Global struct {
	PrintConfigs bool              `toml:"print_configs"`
	Hostname     string            `toml:"hostname"`
	OmitHostname bool              `toml:"omit_hostname"`
	Labels       map[string]string `toml:"labels"`
	Precision    string            `toml:"precision"`
	Interval     Duration          `toml:"interval"`
	Providers    []string          `toml:"providers"`
	Concurrency  int               `toml:"concurrency"`
}

type Log struct {
	FileName   string `toml:"file_name"`
	MaxSize    int    `toml:"max_size"`
	MaxAge     int    `toml:"max_age"`
	MaxBackups int    `toml:"max_backups"`
	LocalTime  bool   `toml:"local_time"`
	Compress   bool   `toml:"compress"`
}

type HTTP struct {
	Enable         bool   `toml:"enable"`
	Address        string `toml:"address"`
	PrintAccess    bool   `toml:"print_access"`
	RunMode        string `toml:"run_mode"`
	IgnoreHostname bool   `toml:"ignore_hostname"`
	// The tag used to name the agent host
	AgentHostTag       string `toml:"agent_host_tag"`
	IgnoreGlobalLabels bool   `toml:"ignore_global_labels"`
	CertFile           string `toml:"cert_file"`
	KeyFile            string `toml:"key_file"`
	ReadTimeout        int    `toml:"read_timeout"`
	WriteTimeout       int    `toml:"write_timeout"`
	IdleTimeout        int    `toml:"idle_timeout"`
}

type Type struct {
	// from console args
	ConfigDir    string
	DebugMode    bool
	DebugLevel   int
	TestMode     bool
	InputFilters string

	// from config.toml
	Global Global `toml:"global"`
	Logs   Logs   `toml:"logs"`
	HTTP   *HTTP  `toml:"http"`
	Log    Log    `toml:"log"`

	HTTPProviderConfig *HTTPProviderConfig `toml:"http_provider"`
}

var Config *Type

func InitConfig(configDir string, debugLevel int, debugMode, testMode bool,
	interval int64, inputFilters, source string) error {
	configFile := path.Join(configDir, "config.toml")
	if !file.IsExist(configFile) {
		return fmt.Errorf("configuration file(%s) not found", configFile)
	}

	Config = &Type{
		ConfigDir:    configDir,
		DebugMode:    debugMode,
		DebugLevel:   debugLevel,
		TestMode:     testMode,
		InputFilters: inputFilters,
	}

	if err := cfg.LoadConfigByDir(configDir, Config); err != nil {
		return fmt.Errorf("failed to load configs of dir: %s err:%w", configDir, err)
	}

	if interval > 0 {
		Config.Global.Interval = Duration(time.Duration(interval) * time.Second)
	}

	if Config.Global.Precision == "" {
		Config.Global.Precision = "ms"
	}

	Config.Global.Hostname = strings.TrimSpace(Config.Global.Hostname)

	if err := InitHostInfo(); err != nil {
		return err
	}

	if Config.Global.PrintConfigs {
		json := jsoniter.ConfigCompatibleWithStandardLibrary
		bs, err := json.MarshalIndent(Config, "", "    ")
		if err != nil {
			fmt.Println(err)
		} else {
			fmt.Println(string(bs))
		}
	}

	// If using test mode, the logs are output to standard output for easy viewing
	if testMode {
		Config.Log.FileName = "stdout"
	}
	if Config.Logs.Topic == "auto" {
		Config.Logs.Topic = source
	}
	var protoPaths, protoFiles, messages []string
	for _, item := range Config.Logs.Items {
		if item == nil {
			continue
		}
		if item.Source == "auto" {
			item.Source = source
		}
		if (platform.CustormLogParse(item.Source)) &&
			(item.ProtoPath == "" || item.ProtoFile == "") {
			return fmt.Errorf("das need set proto path and file")
		}
		if source == platform.CARDAN || source == platform.EBT {
			item.ProcessingRules = []*logs.ProcessingRule{
				{
					Type:    "multi_line",
					Name:    "new_line_with_date",
					Pattern: `[A-Z]*. \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}`,
				},
			}
		}
		protoPaths = append(protoPaths, item.ProtoPath)
		protoFiles = append(protoFiles, item.ProtoFile)
		messages = append(messages, item.Message)
	}
	if len(protoPaths) != 0 {
		protoloader.InitProtoFile(protoPaths, protoFiles, messages)
	}

	return nil
}

func (c *Type) GetHostname() string {
	ret := c.Global.Hostname

	name := HostInfo.GetHostname()
	if ret == "" {
		return name
	}

	ret = strings.Replace(ret, "$hostname", name, -1)
	ret = strings.Replace(ret, "$ip", c.GetHostIP(), -1)
	ret = strings.Replace(ret, "$sn", c.GetHostSN(), -1)
	ret = os.Expand(ret, GetEnv)

	return ret
}

func (c *Type) GetHostIP() string {
	ret := HostInfo.GetIP()
	if ret == "" {
		return c.GetHostname()
	}

	return ret
}

func (c *Type) GetHostSN() string {
	ret := HostInfo.GetSN()
	return ret
}
func GetEnv(key string) string {
	v := os.Getenv(key)
	return envVarEscaper.Replace(v)
}

func GetInterval() time.Duration {
	if Config.Global.Interval <= 0 {
		return time.Second * 15
	}

	return time.Duration(Config.Global.Interval)
}

func GetConcurrency() int {
	if Config.Global.Concurrency <= 0 {
		return runtime.NumCPU() * 10
	}
	return Config.Global.Concurrency
}

func getLocalIP() (net.IP, error) {
	ifs, err := net.Interfaces()
	if err != nil {
		return nil, err
	}

	for _, iface := range ifs {
		if (iface.Flags & net.FlagUp) == 0 {
			continue
		}
		addrs, err := iface.Addrs()
		if err != nil {
			log.Println("W! iface address error", err)
			continue
		}
		for _, addr := range addrs {
			if ip, ok := addr.(*net.IPNet); ok && ip.IP.IsLoopback() {
				continue
			} else {
				ip4 := ip.IP.To4()
				if ip4 == nil {
					continue
				}
				return ip4, nil
			}
		}
	}
	return nil, fmt.Errorf("no local ip found")
}

// Get preferred outbound ip of this machine
func GetOutboundIP() (net.IP, error) {
	ip, err := getLocalIP()
	if err != nil {
		return nil, fmt.Errorf("failed to get local ip: %v", err)
	}
	return ip, nil
}

func GetBiosSn() (string, error) {
	var sn string
	out, err := exec.Command("dmidecode", "-s", "system-serial-number").Output()
	if err != nil {
		return "", fmt.Errorf("failed to get bios sn: %v", err)
	}
	lines := strings.Split(string(out), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "#") {
			continue
		}
		if len(line) > 0 {
			sn = strings.TrimSpace(line)
			break
		}
	}
	return sn, nil
}

func GlobalLabels() map[string]string {
	ret := make(map[string]string)
	for k, v := range Config.Global.Labels {
		ret[k] = Expand(v)
	}
	return ret
}

func Expand(nv string) string {
	nv = strings.Replace(nv, "$hostname", Config.GetHostname(), -1)
	nv = strings.Replace(nv, "$ip", Config.GetHostIP(), -1)
	nv = strings.Replace(nv, "$sn", Config.GetHostSN(), -1)
	nv = os.Expand(nv, GetEnv)
	return nv
}
